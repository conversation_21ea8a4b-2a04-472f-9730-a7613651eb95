//
//  TextTransformController.swift
//  NeonPop
//
//  Created by late night king on 2025/6/17.
//

import SwiftUI

struct TextTransformController: View {
    @ObservedObject var layer: LayerModel
    @ObservedObject var processor: MultiLayerProcessor
    @ObservedObject var undoManager: UndoRedoManager
    let canvasSize: CGSize

    @State private var isDragging = false
    @State private var isScaling = false
    @State private var isRotating = false
    @State private var dragOffset = CGSize.zero
    @State private var lastDragOffset = CGSize.zero
    @State private var scale: CGFloat = 1.0
    @State private var lastScale: CGFloat = 1.0
    @State private var rotation: Double = 0
    @State private var lastRotation: Double = 0
    @State private var isInlineEditing = false
    @State private var textSize: CGSize = .zero


    
    var body: some View {
        ZStack {
            // 文字内容
            textContent
            
            // 变换控制器（只在选中且非编辑状态时显示）
            if layer.isSelected && !isInlineEditing {
                transformControls
            }
        }
        .scaleEffect(scale * layer.transform.scale)
        .rotationEffect(.degrees(rotation + layer.transform.rotation))
        .offset(x: dragOffset.width + layer.transform.position.x,
                y: dragOffset.height + layer.transform.position.y)
        .opacity(layer.transform.opacity)
        .gesture(
            // 在非编辑状态下启用手势（不需要预先选中）
            !isInlineEditing ?
            SimultaneousGesture(
                dragGesture,
                SimultaneousGesture(
                    magnificationGesture,
                    rotationGesture
                )
            ) : nil
        )
        .onTapGesture {
            // 切换选中状态：如果已选中则取消选中，否则选中（只在非编辑状态下）
            if !isInlineEditing {
                if layer.isSelected {
                    processor.deselectAllLayers()
                } else {
                    processor.selectLayer(layer)
                }
            }
        }
        .onTapGesture(count: 2) {
            // 双击进入编辑模式并全选文字（无论当前状态）
            processor.selectLayer(layer)
            processor.hideFloatingMenu() // 隐藏悬浮菜单
            isInlineEditing = true
            // 通知编辑器需要全选文字
            NotificationCenter.default.post(name: .selectAllTextOnEdit, object: layer)
        }
        // 移除 allowsHitTesting 限制，让编辑模式下的双击能正常工作
    }
    
    // 文字内容
    private var textContent: some View {
        ImprovedInlineTextEditor(
            layer: layer,
            processor: processor,
            undoManager: undoManager,
            isEditing: $isInlineEditing
        )
        .background(
            // 隐藏的文字用于测量大小
            Group {
                if let text = layer.text {
                    Text(text)
                        .font(getLayerFont())
                        .multilineTextAlignment(getTextAlignment())
                        .opacity(0)
                        .background(
                            GeometryReader { geometry in
                                Color.clear
                                    .onAppear {
                                        textSize = geometry.size
                                    }
                                    .onChange(of: layer.text) { _ in
                                        textSize = geometry.size
                                    }
                                    .onChange(of: layer.fontSize) { _ in
                                        textSize = geometry.size
                                    }
                                    .onChange(of: layer.fontName) { _ in
                                        textSize = geometry.size
                                    }
                                    .onChange(of: layer.isBold) { _ in
                                        textSize = geometry.size
                                    }
                                    .onChange(of: layer.isItalic) { _ in
                                        textSize = geometry.size
                                    }

                            }
                        )
                }
            }
        )
    }
    
    // 变换控制器
    private var transformControls: some View {
        ZStack {
            let frameWidth = max(textSize.width + 20, 60)
            let frameHeight = max(textSize.height + 20, 40)

            // 选中边框 - 干净简洁的边框
            RoundedRectangle(cornerRadius: 4)
                .stroke(CyberPunkStyle.neonPink, lineWidth: 2)
                .fill(Color.clear)
                .frame(width: frameWidth, height: frameHeight)
                .shadow(color: CyberPunkStyle.neonPink.opacity(0.5), radius: 4)

            // 右上角编辑按钮
            Button(action: {
                showFloatingMenu()
            }) {
                Text("编辑")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(CyberPunkStyle.neonPink)
                            .shadow(color: CyberPunkStyle.neonPink.opacity(0.5), radius: 4)
                    )
            }
            .offset(x: frameWidth/2 + 25, y: -frameHeight/2 - 10)
        }
        .animation(.easeInOut(duration: 0.2), value: layer.isSelected)
    }
    

    
    // 拖拽手势
    private var dragGesture: some Gesture {
        DragGesture()
            .onChanged { value in
                if !isInlineEditing {
                    // 开始拖拽时自动选中图层
                    if !layer.isSelected {
                        processor.selectLayer(layer)
                    }
                    isDragging = true
                    dragOffset = CGSize(
                        width: lastDragOffset.width + value.translation.width,
                        height: lastDragOffset.height + value.translation.height
                    )
                }
            }
            .onEnded { value in
                if !isInlineEditing && isDragging {
                    isDragging = false

                    // 记录撤销操作
                    let oldTransform = layer.transform

                    // 更新图层位置
                    let newX = layer.transform.position.x + dragOffset.width
                    let newY = layer.transform.position.y + dragOffset.height
                    layer.transform.position = CGPoint(x: newX, y: newY)

                    // 记录新变换
                    let newTransform = layer.transform
                    undoManager.recordAction(.transformLayer(layer, oldTransform, newTransform))

                    // 重置拖拽偏移
                    lastDragOffset = .zero
                    dragOffset = .zero

                    // 如果有配对文字，同步位置
                    if layer.pairedLayerId != nil {
                        processor.syncPairedTextPosition(layer)
                    }

                    // 更新合成图像
                    processor.updateLayerTransform(layer, transform: layer.transform)
                }
            }
    }
    
    // 缩放手势
    private var magnificationGesture: some Gesture {
        MagnificationGesture(minimumScaleDelta: 0.1) // 设置最小缩放增量
            .onChanged { value in
                if !isInlineEditing {
                    // 开始缩放时自动选中图层
                    if !layer.isSelected {
                        processor.selectLayer(layer)
                    }
                    isScaling = true
                    scale = lastScale * value
                }
            }
            .onEnded { value in
                if !isInlineEditing && isScaling {
                    isScaling = false

                    // 记录撤销操作
                    let oldTransform = layer.transform

                    // 更新图层缩放
                    layer.transform.scale *= scale
                    layer.transform.scale = max(0.1, layer.transform.scale)

                    // 记录新变换
                    let newTransform = layer.transform
                    undoManager.recordAction(.transformLayer(layer, oldTransform, newTransform))

                    // 重置缩放
                    lastScale = 1.0
                    scale = 1.0

                    // 更新合成图像
                    processor.updateLayerTransform(layer, transform: layer.transform)
                }
            }
    }
    
    // 旋转手势
    private var rotationGesture: some Gesture {
        RotationGesture()
            .onChanged { value in
                if !isInlineEditing {
                    // 开始旋转时自动选中图层
                    if !layer.isSelected {
                        processor.selectLayer(layer)
                    }
                    isRotating = true
                    rotation = lastRotation + value.degrees
                }
            }
            .onEnded { value in
                if !isInlineEditing && isRotating {
                    isRotating = false

                    // 记录撤销操作
                    let oldTransform = layer.transform

                    // 更新图层旋转
                    layer.transform.rotation += rotation

                    // 标准化角度
                    while layer.transform.rotation < 0 {
                        layer.transform.rotation += 360
                    }
                    while layer.transform.rotation >= 360 {
                        layer.transform.rotation -= 360
                    }

                    // 记录新变换
                    let newTransform = layer.transform
                    undoManager.recordAction(.transformLayer(layer, oldTransform, newTransform))

                    // 重置旋转
                    lastRotation = 0
                    rotation = 0

                    // 更新合成图像
                    processor.updateLayerTransform(layer, transform: layer.transform)
                }
            }
    }
    
    // 显示悬浮菜单
    private func showFloatingMenu() {
        // 计算菜单位置（文字上方，距离更远一些）
        let frameHeight = max(textSize.height + 20, 40)
        let menuDistance: CGFloat = 60 // 增加距离
        let menuPosition = CGPoint(
            x: layer.transform.position.x, // 水平居中对齐文字
            y: layer.transform.position.y - frameHeight/2 - menuDistance
        )

        // 使用传入的画布尺寸进行边界检查
        processor.showFloatingMenu(for: layer, at: menuPosition, canvasSize: canvasSize)
    }
    
    // 获取图层字体
    private func getLayerFont() -> Font {
        var font: Font
        
        if layer.fontName == "System" {
            font = .system(size: layer.fontSize)
        } else {
            font = .custom(layer.fontName, size: layer.fontSize)
        }
        
        if layer.isBold && layer.isItalic {
            return font.weight(.bold).italic()
        } else if layer.isBold {
            return font.weight(.bold)
        } else if layer.isItalic {
            return font.italic()
        }
        
        return font
    }
    
    // 获取文字对齐方式
    private func getTextAlignment() -> TextAlignment {
        return layer.textAlignment.swiftUIAlignment
    }
}
