//
//  VideoLoadingView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/20.
//

import SwiftUI

struct VideoLoadingView: View {
    let onCancel: () -> Void
    
    var body: some View {
        ZStack {
            // 背景遮罩
            Color.black.opacity(0.8)
                .ignoresSafeArea()
            
            VStack(spacing: 30) {
                // 加载动画
                loadingAnimation
                
                // 状态信息
                VStack(spacing: 12) {
                    Text("正在加载视频")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(CyberPunkStyle.neonPink)
                    
                    Text("请稍候...")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                }
                
                // 取消按钮
                Button("取消") {
                    onCancel()
                }
                .foregroundColor(.white)
                .padding(.horizontal, 30)
                .padding(.vertical, 12)
                .background(Color.gray.opacity(0.3))
                .cornerRadius(8)
            }
            .padding()
        }
    }
    
    private var loadingAnimation: some View {
        ZStack {
            // 外圈旋转环
            Circle()
                .stroke(
                    LinearGradient(
                        colors: [
                            CyberPunkStyle.electricBlue,
                            CyberPunkStyle.neonPink,
                            CyberPunkStyle.electricBlue
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 4
                )
                .frame(width: 120, height: 120)
                .rotationEffect(.degrees(rotationAngle))
                .shadow(color: CyberPunkStyle.electricBlue.opacity(0.6), radius: 10)
            
            // 内圈脉冲
            Circle()
                .fill(CyberPunkStyle.neonPink.opacity(0.3))
                .frame(width: 80, height: 80)
                .scaleEffect(pulseScale)
                .opacity(pulseOpacity)
            
            // 中心图标
            Image(systemName: "video.badge.plus")
                .font(.system(size: 30, weight: .medium))
                .foregroundColor(.white)
                .shadow(color: .white.opacity(0.8), radius: 5)
            
            // 加载粒子效果
            ForEach(0..<6, id: \.self) { index in
                loadingParticle(for: index)
            }
        }
        .onAppear {
            startAnimations()
        }
    }
    
    @State private var rotationAngle: Double = 0
    @State private var pulseScale: CGFloat = 1.0
    @State private var pulseOpacity: Double = 0.3
    
    private func startAnimations() {
        // 旋转动画
        withAnimation(.linear(duration: 2).repeatForever(autoreverses: false)) {
            rotationAngle = 360
        }
        
        // 脉冲动画
        withAnimation(.easeInOut(duration: 1.2).repeatForever(autoreverses: true)) {
            pulseScale = 1.3
            pulseOpacity = 0.1
        }
    }
    
    private func loadingParticle(for index: Int) -> some View {
        let angle = Double(index) * 60.0 // 每个粒子间隔60度
        let radius: CGFloat = 70
        
        return Circle()
            .fill(CyberPunkStyle.electricBlue)
            .frame(width: 4, height: 4)
            .offset(
                x: cos(Angle(degrees: angle + rotationAngle * 1.5).radians) * radius,
                y: sin(Angle(degrees: angle + rotationAngle * 1.5).radians) * radius
            )
            .opacity(0.8)
            .shadow(color: CyberPunkStyle.electricBlue, radius: 2)
    }
}

#Preview {
    VideoLoadingView(
        onCancel: { }
    )
}
