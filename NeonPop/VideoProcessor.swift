//
//  VideoProcessor.swift
//  NeonPop
//
//  Created by late night king on 2025/6/19.
//

import SwiftUI
import AVFoundation
import Photos
import CoreImage
import Vision
import VisionKit

@MainActor
class VideoProcessor: ObservableObject {
    @Published var isProcessing = false
    @Published var errorMessage: String?
    @Published var processingProgress: Double = 0.0
    @Published var currentStep: String = ""

    private let context = CIContext()

    // 视频帧序列数据
    struct VideoFrameSequence {
        let frames: [UIImage] // 抠图后的透明帧
        let frameRate: Double
        let duration: Double
        let originalSize: CGSize
    }

    // 单帧预览结果
    struct FramePreview {
        let originalFrame: UIImage
        let processedFrame: UIImage
        let frameTime: Double
    }
    
    // 从视频URL获取基本信息
    func getVideoInfo(from url: URL) async -> (duration: Double, thumbnail: UIImage?)? {
        let asset = AVAsset(url: url)
        
        do {
            let duration = try await asset.load(.duration)
            let durationSeconds = CMTimeGetSeconds(duration)
            
            // 生成缩略图
            let imageGenerator = AVAssetImageGenerator(asset: asset)
            imageGenerator.appliesPreferredTrackTransform = true
            
            let time = CMTime(seconds: 1.0, preferredTimescale: 600)
            let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
            let thumbnail = UIImage(cgImage: cgImage)
            
            return (duration: durationSeconds, thumbnail: thumbnail)
        } catch {
            await MainActor.run {
                self.errorMessage = "获取视频信息失败: \(error.localizedDescription)"
            }
            return nil
        }
    }

    // 预览单帧抠图效果
    func previewFrameCutout(from videoURL: URL, at timeSeconds: Double) async -> FramePreview? {
        isProcessing = true
        errorMessage = nil
        processingProgress = 0.0
        currentStep = "正在提取预览帧..."

        let asset = AVAsset(url: videoURL)

        do {
            // 提取单帧
            let imageGenerator = AVAssetImageGenerator(asset: asset)
            imageGenerator.appliesPreferredTrackTransform = true
            imageGenerator.requestedTimeToleranceAfter = .zero
            imageGenerator.requestedTimeToleranceBefore = .zero

            let time = CMTime(seconds: timeSeconds, preferredTimescale: 600)
            let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
            let originalFrame = UIImage(cgImage: cgImage)

            processingProgress = 0.5
            currentStep = "正在处理背景移除..."

            // 对单帧进行背景移除
            if let processedFrame = await removeBackgroundFromFrame(originalFrame) {
                processingProgress = 1.0
                currentStep = "预览完成"
                isProcessing = false

                return FramePreview(
                    originalFrame: originalFrame,
                    processedFrame: processedFrame,
                    frameTime: timeSeconds
                )
            } else {
                errorMessage = "背景移除失败"
                isProcessing = false
                return nil
            }
        } catch {
            errorMessage = "提取预览帧失败: \(error.localizedDescription)"
            isProcessing = false
            return nil
        }
    }

    // 处理视频：转换为抠图后的序列帧
    func processVideoToFrameSequence(from videoURL: URL, startTime: Double, endTime: Double) async -> VideoFrameSequence? {
        isProcessing = true
        errorMessage = nil
        processingProgress = 0.0
        currentStep = "正在分析视频..."

        let asset = AVAsset(url: videoURL)

        do {
            // 获取视频信息
            let duration = try await asset.load(.duration)
            let tracks = try await asset.loadTracks(withMediaType: .video)
            guard let videoTrack = tracks.first else {
                errorMessage = "无法找到视频轨道"
                isProcessing = false
                return nil
            }

            let naturalSize = try await videoTrack.load(.naturalSize)
            let frameRate = try await videoTrack.load(.nominalFrameRate)

            currentStep = "正在提取视频帧..."
            processingProgress = 0.1

            // 提取视频帧
            let frames = try await extractFrames(from: asset, startTime: startTime, endTime: endTime, frameRate: Double(frameRate))

            currentStep = "正在处理背景移除..."
            processingProgress = 0.3

            // 对每一帧进行背景移除
            let processedFrames = await processFramesWithBackgroundRemoval(frames)

            processingProgress = 1.0
            currentStep = "处理完成"
            isProcessing = false

            return VideoFrameSequence(
                frames: processedFrames,
                frameRate: Double(frameRate),
                duration: endTime - startTime,
                originalSize: naturalSize
            )

        } catch {
            errorMessage = "视频处理失败: \(error.localizedDescription)"
            isProcessing = false
            return nil
        }
    }

    // 提取视频帧
    private func extractFrames(from asset: AVAsset, startTime: Double, endTime: Double, frameRate: Double) async throws -> [UIImage] {
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        imageGenerator.requestedTimeToleranceAfter = .zero
        imageGenerator.requestedTimeToleranceBefore = .zero

        // 计算需要提取的帧数（最多30帧，确保性能）
        let duration = endTime - startTime
        let maxFrames = 30
        let frameInterval = max(duration / Double(maxFrames), 1.0 / frameRate)

        var frames: [UIImage] = []
        var currentTime = startTime

        while currentTime < endTime {
            let time = CMTime(seconds: currentTime, preferredTimescale: 600)

            do {
                let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
                let uiImage = UIImage(cgImage: cgImage)
                frames.append(uiImage)

                // 更新进度
                let progress = 0.1 + (currentTime - startTime) / duration * 0.2
                await MainActor.run {
                    self.processingProgress = progress
                }

            } catch {
                print("提取帧失败 at \(currentTime): \(error)")
            }

            currentTime += frameInterval
        }

        return frames
    }
    
    // 对帧序列进行背景移除处理
    private func processFramesWithBackgroundRemoval(_ frames: [UIImage]) async -> [UIImage] {
        var processedFrames: [UIImage] = []

        for (index, frame) in frames.enumerated() {
            // 更新进度
            let progress = 0.3 + (Double(index) / Double(frames.count)) * 0.6
            await MainActor.run {
                self.processingProgress = progress
                self.currentStep = "正在处理第 \(index + 1)/\(frames.count) 帧..."
            }

            // 对当前帧进行背景移除
            if let processedFrame = await removeBackgroundFromFrame(frame) {
                processedFrames.append(processedFrame)
            } else {
                // 如果背景移除失败，使用原帧
                processedFrames.append(frame)
            }
        }

        return processedFrames
    }

    // 对单帧进行背景移除
    private func removeBackgroundFromFrame(_ image: UIImage) async -> UIImage? {
        guard #available(iOS 17.0, *) else {
            return image // iOS 17以下版本直接返回原图
        }

        guard let cgImage = image.cgImage else { return nil }

        let requestHandler = VNImageRequestHandler(cgImage: cgImage, options: [:])

        return await withCheckedContinuation { continuation in
            let request = VNGenerateForegroundInstanceMaskRequest { request, error in
                if let error = error {
                    print("背景移除失败: \(error)")
                    continuation.resume(returning: image) // 失败时返回原图
                    return
                }

                guard let result = request.results?.first as? VNInstanceMaskObservation else {
                    continuation.resume(returning: image)
                    return
                }

                do {
                    let maskedPixelBuffer = try result.generateMaskedImage(
                        ofInstances: result.allInstances,
                        from: requestHandler,
                        croppedToInstancesExtent: false
                    )

                    let processedImage = self.correctImageOrientation(from: maskedPixelBuffer, originalImage: image)
                    continuation.resume(returning: processedImage)
                } catch {
                    print("生成遮罩图像失败: \(error)")
                    continuation.resume(returning: image)
                }
            }

            do {
                try requestHandler.perform([request])
            } catch {
                print("执行背景移除请求失败: \(error)")
                continuation.resume(returning: image)
            }
        }
    }

    // 修正图像方向（从ImageProcessor复制）
    private func correctImageOrientation(from pixelBuffer: CVPixelBuffer, originalImage: UIImage) -> UIImage {
        let ciImage = CIImage(cvPixelBuffer: pixelBuffer)

        guard let cgImage = context.createCGImage(ciImage, from: ciImage.extent) else {
            return originalImage
        }

        return UIImage(cgImage: cgImage, scale: originalImage.scale, orientation: originalImage.imageOrientation)
    }
    
    // 生成临时视频文件URL
    private func getTemporaryVideoURL() -> URL {
        let tempDirectory = FileManager.default.temporaryDirectory
        let fileName = "neonpop_video_\(UUID().uuidString).mp4"
        return tempDirectory.appendingPathComponent(fileName)
    }
    
    // 清理临时文件
    func cleanupTemporaryFiles() {
        let tempDirectory = FileManager.default.temporaryDirectory
        do {
            let tempFiles = try FileManager.default.contentsOfDirectory(at: tempDirectory, includingPropertiesForKeys: nil)
            for file in tempFiles {
                if file.lastPathComponent.hasPrefix("neonpop_video_") {
                    try? FileManager.default.removeItem(at: file)
                }
            }
        } catch {
            print("清理临时文件失败: \(error)")
        }
    }
}

// 视频处理错误类型
enum VideoProcessingError: Error, LocalizedError {
    case invalidVideo
    case exportFailed
    case unsupportedFormat
    
    var errorDescription: String? {
        switch self {
        case .invalidVideo:
            return "无效的视频文件"
        case .exportFailed:
            return "视频导出失败"
        case .unsupportedFormat:
            return "不支持的视频格式"
        }
    }
}
