//
//  VideoLayerView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/19.
//

import SwiftUI
import AVFoundation
import AVKit

struct VideoLayerView: View {
    let layer: LayerModel
    @State private var player: AVPlayer?
    @State private var playerLayer: AVPlayerLayer?
    @State private var isPlaying = false
    
    var body: some View {
        Group {
            if let player = player {
                VideoPlayerView(player: player, layer: layer)
                    .onAppear {
                        setupPlayer()
                        startPlayback()
                    }
                    .onDisappear {
                        stopPlayback()
                    }
            } else {
                // 显示缩略图作为占位符
                if let thumbnail = layer.videoThumbnail {
                    Image(uiImage: thumbnail)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .onAppear {
                            setupPlayer()
                        }
                } else {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .overlay(
                            Image(systemName: "video")
                                .font(.largeTitle)
                                .foregroundColor(.white.opacity(0.7))
                        )
                }
            }
        }
        .opacity(layer.transform.opacity)
        .scaleEffect(layer.transform.scale)
        .rotationEffect(.degrees(layer.transform.rotation))
        .colorMultiply(layer.isColorTransformed ? CyberPunkStyle.neonPink : .white)
    }
    
    private func setupPlayer() {
        guard let videoURL = layer.videoURL else { return }
        
        let asset = AVAsset(url: videoURL)
        let playerItem = AVPlayerItem(asset: asset)
        
        // 设置播放时间范围
        let startTime = CMTime(seconds: layer.videoStartTime, preferredTimescale: 600)
        let endTime = CMTime(seconds: layer.videoEndTime, preferredTimescale: 600)
        let timeRange = CMTimeRange(start: startTime, end: endTime)
        
        // 创建播放器
        player = AVPlayer(playerItem: playerItem)
        
        // 设置循环播放
        if layer.isLooping {
            setupLooping()
        }
    }
    
    private func setupLooping() {
        guard let player = player else { return }
        
        // 监听播放结束
        NotificationCenter.default.addObserver(
            forName: .AVPlayerItemDidPlayToEndTime,
            object: player.currentItem,
            queue: .main
        ) { _ in
            // 重新开始播放
            let startTime = CMTime(seconds: layer.videoStartTime, preferredTimescale: 600)
            player.seek(to: startTime) { _ in
                if self.isPlaying {
                    player.play()
                }
            }
        }
    }
    
    private func startPlayback() {
        guard let player = player else { return }
        
        let startTime = CMTime(seconds: layer.videoStartTime, preferredTimescale: 600)
        player.seek(to: startTime) { _ in
            player.play()
            self.isPlaying = true
        }
    }
    
    private func stopPlayback() {
        player?.pause()
        isPlaying = false
    }
}

// 自定义视频播放器视图
struct VideoPlayerView: UIViewRepresentable {
    let player: AVPlayer
    let layer: LayerModel
    
    func makeUIView(context: Context) -> UIView {
        let view = UIView()
        view.backgroundColor = .clear
        
        let playerLayer = AVPlayerLayer(player: player)
        playerLayer.videoGravity = .resizeAspect
        view.layer.addSublayer(playerLayer)
        
        return view
    }
    
    func updateUIView(_ uiView: UIView, context: Context) {
        if let playerLayer = uiView.layer.sublayers?.first as? AVPlayerLayer {
            playerLayer.frame = uiView.bounds
            
            // 应用颜色变换
            if layer.isColorTransformed {
                // 创建颜色滤镜
                let colorFilter = CALayer()
                colorFilter.backgroundColor = UIColor(CyberPunkStyle.neonPink).cgColor
                colorFilter.compositingFilter = "multiplyBlendMode"
                colorFilter.frame = uiView.bounds
                
                // 移除旧的滤镜
                uiView.layer.sublayers?.removeAll { $0 != playerLayer }
                
                // 添加新的滤镜
                uiView.layer.addSublayer(colorFilter)
            } else {
                // 移除颜色滤镜，只保留播放器层
                uiView.layer.sublayers?.removeAll { $0 != playerLayer }
            }
        }
    }
}

// 视频图层编辑视图
struct VideoLayerEditingView: View {
    @ObservedObject var layer: LayerModel
    @ObservedObject var processor: MultiLayerProcessor
    @ObservedObject var undoManager: UndoRedoManager
    let canvasSize: CGSize
    
    @State private var dragOffset = CGSize.zero
    @State private var isSelected = false
    
    var body: some View {
        VideoLayerView(layer: layer)
            .frame(width: getVideoDisplaySize().width, height: getVideoDisplaySize().height)
            .position(
                x: canvasSize.width / 2 + layer.transform.position.x + dragOffset.width,
                y: canvasSize.height / 2 + layer.transform.position.y + dragOffset.height
            )
            .overlay(
                // 选择框
                Group {
                    if layer.isSelected {
                        Rectangle()
                            .stroke(CyberPunkStyle.neonPink, lineWidth: 2)
                            .background(Color.clear)
                    }
                }
            )
            .gesture(
                DragGesture()
                    .onChanged { value in
                        dragOffset = value.translation
                    }
                    .onEnded { value in
                        // 更新图层位置
                        let newPosition = CGPoint(
                            x: layer.transform.position.x + dragOffset.width,
                            y: layer.transform.position.y + dragOffset.height
                        )

                        let oldTransform = layer.transform
                        var newTransform = layer.transform
                        newTransform.position = newPosition

                        processor.updateLayerTransform(layer, transform: newTransform)
                        undoManager.recordAction(.transformLayer(layer, oldTransform, newTransform))

                        // 重置拖拽状态
                        dragOffset = .zero
                    }
            )
            .onTapGesture {
                // 选择图层
                processor.selectLayer(layer)
            }
            .onAppear {
                isSelected = layer.isSelected
            }
            .onChange(of: layer.isSelected) { newValue in
                isSelected = newValue
            }
    }
    
    private func getVideoDisplaySize() -> CGSize {
        guard let thumbnail = layer.videoThumbnail else {
            return CGSize(width: 200, height: 150)
        }
        
        let imageSize = thumbnail.size
        let maxDimension = max(canvasSize.width, canvasSize.height) * 0.8
        let scale = min(maxDimension / imageSize.width, maxDimension / imageSize.height)
        
        return CGSize(
            width: imageSize.width * scale,
            height: imageSize.height * scale
        )
    }
}

#Preview {
    let layer = LayerModel(name: "测试视频", type: .video)
    layer.videoURL = URL(string: "https://example.com/video.mp4")
    layer.isLooping = true
    
    return VideoLayerView(layer: layer)
        .frame(width: 300, height: 200)
        .background(Color.black)
}
