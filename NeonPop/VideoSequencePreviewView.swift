//
//  VideoSequencePreviewView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/20.
//

import SwiftUI

struct VideoSequencePreviewView: View {
    let frameSequence: VideoProcessor.VideoFrameSequence
    let onConfirm: () -> Void
    let onRetry: () -> Void
    let onCancel: () -> Void
    
    @State private var currentFrameIndex = 0
    @State private var isPlaying = false
    @State private var playbackTimer: Timer?
    @State private var showingOriginal = false
    @State private var previewFrames: [UIImage] = [] // 预处理的预览帧
    @State private var isPreprocessing = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("序列帧预览")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(CyberPunkStyle.neonPink)
                    .padding(.top)
                
                // 播放控制
                HStack(spacing: 20) {
                    Button(action: togglePlayback) {
                        Image(systemName: isPlaying ? "pause.circle.fill" : "play.circle.fill")
                            .font(.system(size: 40))
                            .foregroundColor(isPreprocessing ? .gray : CyberPunkStyle.electricBlue)
                    }
                    .disabled(isPreprocessing)
                    
                    VStack(spacing: 4) {
                        Text("帧 \(currentFrameIndex + 1) / \(frameSequence.frames.count)")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                        
                        Text("\(frameSequence.frameRate, specifier: "%.1f") fps")
                            .font(.system(size: 14))
                            .foregroundColor(.white.opacity(0.7))
                    }
                    
                    Button("重置") {
                        stopPlayback()
                        currentFrameIndex = 0
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(Color.gray.opacity(0.3))
                    .cornerRadius(8)
                }
                
                // 预览区域
                VStack(spacing: 16) {
                    // 切换按钮
                    HStack(spacing: 20) {
                        Button(action: { showingOriginal = false }) {
                            Text("抠图后")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(showingOriginal ? .white.opacity(0.6) : CyberPunkStyle.neonPink)
                                .padding(.horizontal, 20)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(showingOriginal ? Color.clear : CyberPunkStyle.neonPink.opacity(0.2))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 8)
                                                .stroke(showingOriginal ? Color.clear : CyberPunkStyle.neonPink, lineWidth: 1)
                                        )
                                )
                        }
                        
                        Button(action: { showingOriginal = true }) {
                            Text("原始帧")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(showingOriginal ? CyberPunkStyle.neonPink : .white.opacity(0.6))
                                .padding(.horizontal, 20)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(showingOriginal ? CyberPunkStyle.neonPink.opacity(0.2) : Color.clear)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 8)
                                                .stroke(showingOriginal ? CyberPunkStyle.neonPink : Color.clear, lineWidth: 1)
                                        )
                                )
                        }
                    }
                    
                    // 预览帧
                    ZStack {
                        // 透明网格背景（仅抠图后显示）
                        if !showingOriginal {
                            TransparencyGrid()
                        }
                        
                        if isPreprocessing {
                            // 预处理中显示加载指示器
                            VStack(spacing: 16) {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: CyberPunkStyle.neonPink))
                                    .scaleEffect(1.5)

                                Text("正在优化预览...")
                                    .foregroundColor(.white.opacity(0.7))
                            }
                            .frame(height: 400)
                        } else if !previewFrames.isEmpty && currentFrameIndex < previewFrames.count {
                            // 使用预处理的帧
                            Image(uiImage: previewFrames[currentFrameIndex])
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(maxHeight: 400)
                                .cornerRadius(12)
                                .shadow(color: .black.opacity(0.3), radius: 10)
                        } else if currentFrameIndex < frameSequence.frames.count {
                            // 回退到原始帧
                            Image(uiImage: frameSequence.frames[currentFrameIndex])
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(maxHeight: 400)
                                .cornerRadius(12)
                                .shadow(color: .black.opacity(0.3), radius: 10)
                        }
                    }
                    .animation(.easeInOut(duration: 0.3), value: showingOriginal)
                }
                .padding(.horizontal)
                
                // 进度条
                VStack(spacing: 8) {
                    Slider(
                        value: Binding(
                            get: { Double(currentFrameIndex) },
                            set: { newValue in
                                stopPlayback()
                                currentFrameIndex = Int(newValue)
                            }
                        ),
                        in: 0...Double(frameSequence.frames.count - 1),
                        step: 1
                    )
                    .accentColor(CyberPunkStyle.electricBlue)
                    
                    Text("拖动查看不同帧")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                }
                .padding(.horizontal)
                
                // 提示文字
                Text("请检查抠图效果是否满意")
                    .font(.system(size: 16))
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                Spacer()
                
                // 底部按钮
                VStack(spacing: 16) {
                    // 主要操作按钮
                    HStack(spacing: 20) {
                        Button("重新处理") {
                            stopPlayback()
                            onRetry()
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(Color.orange.opacity(0.8))
                        .cornerRadius(8)
                        
                        Button("确认添加到画布") {
                            stopPlayback()
                            onConfirm()
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(CyberPunkStyle.electricBlue)
                        .cornerRadius(8)
                    }
                    
                    // 取消按钮
                    Button("取消") {
                        stopPlayback()
                        onCancel()
                    }
                    .foregroundColor(.white.opacity(0.7))
                    .padding(.horizontal, 30)
                    .padding(.vertical, 10)
                    .background(Color.gray.opacity(0.3))
                    .cornerRadius(8)
                }
                .padding(.bottom)
            }
            .background(Color.black)
            .navigationBarHidden(true)
        }
        .onAppear {
            preprocessFrames()
        }
        .onDisappear {
            stopPlayback()
        }
    }
    
    private func togglePlayback() {
        if isPlaying {
            stopPlayback()
        } else {
            startPlayback()
        }
    }
    
    private func startPlayback() {
        stopPlayback() // 先停止现有定时器

        isPlaying = true
        // 限制预览播放帧率，最大15fps，减少卡顿
        let maxPreviewFPS = 15.0
        let previewFrameRate = min(frameSequence.frameRate, maxPreviewFPS)
        let frameInterval = 1.0 / previewFrameRate

        print("🎬 开始预览播放 - 原始帧率: \(frameSequence.frameRate), 预览帧率: \(previewFrameRate)")

        playbackTimer = Timer.scheduledTimer(withTimeInterval: frameInterval, repeats: true) { _ in
            DispatchQueue.main.async {
                self.currentFrameIndex = (self.currentFrameIndex + 1) % self.frameSequence.frames.count
            }
        }
    }
    
    private func stopPlayback() {
        isPlaying = false
        playbackTimer?.invalidate()
        playbackTimer = nil
    }

    // 预处理帧以提高播放性能
    private func preprocessFrames() {
        guard previewFrames.isEmpty else { return }

        isPreprocessing = true

        Task {
            let processedFrames = await withTaskGroup(of: (Int, UIImage?).self) { group in
                var results: [(Int, UIImage?)] = []

                // 并行处理帧
                for (index, frame) in frameSequence.frames.enumerated() {
                    group.addTask {
                        let resizedFrame = await self.resizeImageForPreview(frame)
                        return (index, resizedFrame)
                    }
                }

                for await result in group {
                    results.append(result)
                }

                return results.sorted { $0.0 < $1.0 }.compactMap { $0.1 }
            }

            await MainActor.run {
                self.previewFrames = processedFrames
                self.isPreprocessing = false
                print("🎬 预处理完成 - 处理了 \(processedFrames.count) 帧")
            }
        }
    }

    // 调整图片大小以提高性能
    private func resizeImageForPreview(_ image: UIImage) async -> UIImage? {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                // 限制预览图片的最大尺寸
                let maxSize: CGFloat = 400
                let scale = min(maxSize / image.size.width, maxSize / image.size.height, 1.0)

                if scale < 1.0 {
                    let newSize = CGSize(
                        width: image.size.width * scale,
                        height: image.size.height * scale
                    )

                    UIGraphicsBeginImageContextWithOptions(newSize, false, 0.0)
                    image.draw(in: CGRect(origin: .zero, size: newSize))
                    let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
                    UIGraphicsEndImageContext()

                    continuation.resume(returning: resizedImage)
                } else {
                    continuation.resume(returning: image)
                }
            }
        }
    }
}

#Preview {
    let frameSequence = VideoProcessor.VideoFrameSequence(
        frames: [
            UIImage(systemName: "1.circle.fill")!,
            UIImage(systemName: "2.circle.fill")!,
            UIImage(systemName: "3.circle.fill")!
        ],
        frameRate: 2.0,
        duration: 1.5,
        originalSize: CGSize(width: 100, height: 100)
    )
    
    return VideoSequencePreviewView(
        frameSequence: frameSequence,
        onConfirm: { },
        onRetry: { },
        onCancel: { }
    )
}
