//
//  VideoPlaybackTestView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/20.
//

import SwiftUI

struct VideoPlaybackTestView: View {
    @ObservedObject var layer: LayerModel
    @State private var currentFrameIndex = 0
    @State private var timer: Timer?
    
    var body: some View {
        VStack(spacing: 20) {
            Text("视频播放测试")
                .font(.title)
                .foregroundColor(.white)
            
            if layer.type == .video && !layer.frameSequence.isEmpty {
                VStack(spacing: 10) {
                    // 显示当前帧
                    Image(uiImage: layer.frameSequence[currentFrameIndex])
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxWidth: 300, maxHeight: 300)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(8)
                    
                    // 帧信息
                    Text("帧 \(currentFrameIndex + 1) / \(layer.frameSequence.count)")
                        .foregroundColor(.white)
                    
                    Text("帧率: \(layer.frameRate, specifier: "%.1f") fps")
                        .foregroundColor(.white.opacity(0.7))
                    
                    // 控制按钮
                    HStack(spacing: 20) {
                        Button("开始播放") {
                            startPlayback()
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 8)
                        .background(Color.green)
                        .cornerRadius(8)
                        
                        Button("停止播放") {
                            stopPlayback()
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 8)
                        .background(Color.red)
                        .cornerRadius(8)
                        
                        Button("下一帧") {
                            nextFrame()
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 8)
                        .background(Color.blue)
                        .cornerRadius(8)
                    }
                }
            } else {
                Text("没有视频数据")
                    .foregroundColor(.white.opacity(0.7))
            }
        }
        .padding()
        .background(Color.black)
        .onDisappear {
            stopPlayback()
        }
    }
    
    private func startPlayback() {
        stopPlayback() // 先停止现有定时器
        
        let frameInterval = 1.0 / layer.frameRate
        print("🎬 测试播放开始 - 帧率: \(layer.frameRate), 间隔: \(frameInterval)秒")
        
        timer = Timer.scheduledTimer(withTimeInterval: frameInterval, repeats: true) { _ in
            DispatchQueue.main.async {
                self.currentFrameIndex = (self.currentFrameIndex + 1) % self.layer.frameSequence.count
                print("🎬 测试播放 - 切换到帧 \(self.currentFrameIndex)")
            }
        }
    }
    
    private func stopPlayback() {
        timer?.invalidate()
        timer = nil
        print("🎬 测试播放停止")
    }
    
    private func nextFrame() {
        currentFrameIndex = (currentFrameIndex + 1) % layer.frameSequence.count
        print("🎬 手动切换到帧 \(currentFrameIndex)")
    }
}

#Preview {
    let layer = LayerModel(name: "测试视频", type: .video)
    // 创建一些测试帧
    layer.frameSequence = [
        UIImage(systemName: "1.circle.fill")!,
        UIImage(systemName: "2.circle.fill")!,
        UIImage(systemName: "3.circle.fill")!
    ]
    layer.frameRate = 2.0
    
    return VideoPlaybackTestView(layer: layer)
}
