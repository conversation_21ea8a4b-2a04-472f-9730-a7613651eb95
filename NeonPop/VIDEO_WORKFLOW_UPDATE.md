# 视频处理流程更新

## 新的视频导入流程

### 1. 导入视频 → 选择时间段
- 用户选择视频文件
- 显示 `VideoLoadingView` 加载指示器
- 打开 `VideoTrimView` 让用户选择时间段（最多10秒）

### 2. 预览抠图效果
- 用户点击"预览抠图"按钮
- 系统提取选择时间段的中间帧
- 对该帧进行背景移除处理
- 显示 `VideoPreviewView` 对比原图和抠图效果

### 3. 用户确认
在预览界面，用户可以选择：
- **确认批量处理**：满意抠图效果，开始处理所有帧
- **重新抠图**：不满意效果，重新处理预览帧
- **取消**：放弃当前操作

### 4. 批量处理
- 用户确认后，显示 `VideoProcessingView` 进度界面
- 处理所有选择时间段的帧
- 完成后添加到画布并开始循环播放

## 主要代码更新

### 1. VideoProcessor.swift
```swift
// 新增预览功能
struct FramePreview {
    let originalFrame: UIImage
    let processedFrame: UIImage
    let frameTime: Double
}

func previewFrameCutout(from videoURL: URL, at timeSeconds: Double) async -> FramePreview?
```

### 2. VideoPreviewView.swift
- 新建预览确认界面
- 支持原图/抠图效果切换对比
- 透明网格背景显示抠图效果
- 提供确认、重试、取消操作

### 3. VideoTrimView.swift
```swift
// 新增状态
@State private var showingPreview = false
@State private var framePreview: VideoProcessor.FramePreview?

// 修改按钮文字
Button("预览抠图") {
    previewCutout()
}

// 新增预览方法
private func previewCutout()
```

### 4. VideoLoadingView.swift
- 新建视频加载指示器
- 解决"导入视频没反应"的问题
- 显示加载动画和取消按钮

### 5. MultiLayerProcessor.swift
```swift
// 视频播放定时器管理
private var videoTimers: [UUID: Timer] = [:]

// 改进的视频播放逻辑
private func startVideoPlayback(for layer: LayerModel)
private func stopVideoPlayback(for layer: LayerModel)
```

## 视频循环播放修复

### 问题原因
1. 定时器没有正确保存引用
2. 定时器可能被过早释放
3. 帧率计算可能有问题

### 解决方案
1. **定时器管理**：使用 `videoTimers` 字典保存定时器引用
2. **帧率限制**：最大30fps，避免过高帧率影响性能
3. **生命周期管理**：图层删除时正确停止定时器
4. **调试日志**：添加启动/停止日志便于调试

```swift
// 启动视频播放定时器
private func startVideoPlayback(for layer: LayerModel) {
    guard layer.type == .video && !layer.frameSequence.isEmpty else { return }

    // 停止现有定时器（如果有）
    stopVideoPlayback(for: layer)

    let frameInterval = max(1.0 / layer.frameRate, 1.0 / 30.0) // 最大30fps

    let timer = Timer.scheduledTimer(withTimeInterval: frameInterval, repeats: true) { timer in
        Task { @MainActor in
            // 检查图层是否还存在
            guard self.layers.contains(where: { $0.id == layer.id }) else {
                timer.invalidate()
                self.videoTimers.removeValue(forKey: layer.id)
                return
            }

            // 更新当前帧索引
            layer.currentFrameIndex = (layer.currentFrameIndex + 1) % layer.frameSequence.count

            // 更新合成图像
            await self.updateCompositeImage()
        }
    }
    
    // 保存定时器引用
    videoTimers[layer.id] = timer
}
```

## 用户体验改进

### 之前的问题
1. 导入视频没有任何反馈
2. 直接批量处理，用户无法预知抠图效果
3. 视频在画布上不循环播放
4. 无法重新调整抠图效果

### 现在的体验
1. **加载反馈**：视频导入时显示加载动画
2. **预览确认**：先看单帧效果再决定是否批量处理
3. **循环播放**：视频序列帧在画布上正确循环播放
4. **可重试**：不满意抠图效果可以重新处理
5. **透明显示**：网格背景清晰显示抠图的透明效果

## 技术细节

### 预览时间点选择
- 选择时间段的中间点：`startTime + (endTime - startTime) / 2`
- 确保预览帧代表性强

### 定时器性能优化
- 最大帧率限制为30fps
- 使用 `Task { @MainActor in }` 确保UI更新在主线程
- 图层删除时及时清理定时器

### 错误处理
- 预览失败时显示错误信息
- 批量处理失败时回退到预览状态
- 提供取消和重试选项

## 测试建议

1. **导入不同格式的视频**测试兼容性
2. **测试不同时长的视频**（1秒到10秒）
3. **测试抠图效果**在不同背景的视频上
4. **测试循环播放**确保视频在画布上正确循环
5. **测试多个视频图层**同时播放的性能
