//
//  FloatingTextMenu.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI

struct FloatingTextMenu: View {
    @ObservedObject var layer: LayerModel
    @ObservedObject var processor: MultiLayerProcessor
    @ObservedObject var undoManager: UndoRedoManager
    @Binding var isVisible: Bool

    let position: CGPoint

    @State private var showingColorPicker = false
    @State private var showingDetailEditor = false

    // 检测是否为粉色标题文字
    private var isTitleText: Bool {
        return layer.textColor == Color(CyberPunkStyle.neonPink)
    }
    
    var body: some View {
        if isVisible {
            VStack(spacing: 8) {
                // 第一行：编辑和样式按钮
                quickEditButtons

                // 第二行：字体大小控制
                fontSizeControls

                // 第三行：字体、排列和模糊控制
                advancedControls

                // 第四行：配对控制
                pairingControls
            }
            .padding(12) // 增加内边距
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.black.opacity(0.9))
                    .shadow(color: CyberPunkStyle.neonPink.opacity(0.3), radius: 8)
            )
            .offset(x: position.x, y: position.y)

            .sheet(isPresented: $showingDetailEditor) {
                TextEditingView(layer: layer, processor: processor, undoManager: undoManager)
            }
        }
    }
    

    
    // 快速编辑按钮 - 更大的按钮
    private var quickEditButtons: some View {
        HStack(spacing: 10) {
            // 编辑文字按钮
            Button(action: {
                processor.startInlineTextEditing(for: layer)
            }) {
                Image(systemName: "pencil")
                    .foregroundColor(CyberPunkStyle.neonPink)
                    .font(.system(size: 16, weight: .medium))
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(CyberPunkStyle.neonPink.opacity(0.2))
                    )
            }

            // 粗体按钮
            Button(action: {
                toggleBold()
            }) {
                Text("B")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(layer.isBold ? CyberPunkStyle.neonPink : .white.opacity(0.8))
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(layer.isBold ? CyberPunkStyle.neonPink.opacity(0.2) : Color.gray.opacity(0.3))
                    )
            }

            // 斜体按钮
            Button(action: {
                toggleItalic()
            }) {
                Text("I")
                    .font(.system(size: 18, weight: .medium))
                    .italic()
                    .foregroundColor(layer.isItalic ? CyberPunkStyle.neonPink : .white.opacity(0.8))
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(layer.isItalic ? CyberPunkStyle.neonPink.opacity(0.2) : Color.gray.opacity(0.3))
                    )
            }

            // 颜色选择按钮
            Button(action: {
                showingColorPicker.toggle()
            }) {
                Circle()
                    .fill(layer.textColor)
                    .frame(width: 24, height: 24)
                    .overlay(
                        Circle()
                            .stroke(.white, lineWidth: 2)
                    )
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(Color.gray.opacity(0.3))
                    )
            }
            .popover(isPresented: $showingColorPicker) {
                colorPickerPopover
            }


        }
    }

    // 字体大小控制 - 单独一行
    private var fontSizeControls: some View {
        HStack(spacing: 12) {
            // 字体大小减小按钮
            Button(action: {
                decreaseFontSize()
            }) {
                Image(systemName: "minus")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(Color.gray.opacity(0.3))
                    )
            }

            // 字体大小显示
            Text("\(Int(layer.fontSize))")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .frame(minWidth: 40)

            // 字体大小增大按钮
            Button(action: {
                increaseFontSize()
            }) {
                Image(systemName: "plus")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(Color.gray.opacity(0.3))
                    )
            }
        }
    }

    // 高级控制 - 第三行
    private var advancedControls: some View {
        HStack(spacing: 10) {
            // 字体切换按钮
            Button(action: {
                toggleFont()
            }) {
                Text("字体")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(Color.gray.opacity(0.3))
                    )
            }

            // 横竖排列切换按钮
            Button(action: {
                toggleVertical()
            }) {
                Image(systemName: layer.isVertical ? "text.alignleft" : "text.aligncenter")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(layer.isVertical ? CyberPunkStyle.neonPink : .white.opacity(0.8))
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(layer.isVertical ? CyberPunkStyle.neonPink.opacity(0.2) : Color.gray.opacity(0.3))
                    )
            }

            // 高斯模糊切换按钮（只有非粉色标题文字才显示）
            if !isTitleText {
                Button(action: {
                    toggleBlur()
                }) {
                    Image(systemName: "camera.filters")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(layer.hasBlur ? CyberPunkStyle.neonPink : .white.opacity(0.8))
                        .frame(width: 40, height: 40)
                        .background(
                            Circle()
                                .fill(layer.hasBlur ? CyberPunkStyle.neonPink.opacity(0.2) : Color.gray.opacity(0.3))
                        )
                }

                // 模糊程度调节（只在开启模糊时显示）
                if layer.hasBlur {
                    HStack(spacing: 4) {
                        Button(action: {
                            decreaseBlurRadius()
                        }) {
                            Image(systemName: "minus.circle")
                                .font(.system(size: 12))
                                .foregroundColor(layer.blurRadius > 0 ? .white.opacity(0.8) : .white.opacity(0.4))
                                .frame(width: 30, height: 30)
                        }
                        .disabled(layer.blurRadius <= 0)

                        Text("\(Int(layer.blurRadius))")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(CyberPunkStyle.neonPink)
                            .frame(minWidth: 20)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.black.opacity(0.3))
                            )

                        Button(action: {
                            increaseBlurRadius()
                        }) {
                            Image(systemName: "plus.circle")
                                .font(.system(size: 12))
                                .foregroundColor(layer.blurRadius < 20 ? .white.opacity(0.8) : .white.opacity(0.4))
                                .frame(width: 30, height: 30)
                        }
                        .disabled(layer.blurRadius >= 20)
                    }
                }
            }
        }
    }

    // 颜色选择器弹窗
    private var colorPickerPopover: some View {
        VStack(spacing: 12) {
            Text("选择颜色")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
            
            // 预设颜色
            LazyVGrid(columns: Array(repeating: GridItem(.fixed(40)), count: 4), spacing: 8) {
                ForEach(presetColors, id: \.self) { color in
                    Button(action: {
                        changeTextColor(to: color)
                        showingColorPicker = false
                    }) {
                        Circle()
                            .fill(color)
                            .frame(width: 32, height: 32)
                            .overlay(
                                Circle()
                                    .stroke(layer.textColor == color ? .white : .clear, lineWidth: 2)
                            )
                    }
                }
            }
            
            // 自定义颜色选择器
            ColorPicker("自定义颜色", selection: Binding(
                get: { layer.textColor },
                set: { changeTextColor(to: $0) }
            ))
            .foregroundColor(.white)
        }
        .padding()
        .background(Color.black.opacity(0.9))
        .cornerRadius(12)
    }
    
    // 预设颜色
    private let presetColors: [Color] = [
        .white,
        CyberPunkStyle.neonPink,
        CyberPunkStyle.decorativeBlue,
        CyberPunkStyle.electricBlue,
        .red,
        .orange,
        .yellow,
        .green,
        .blue,
        .purple,
        .gray,
        .black
    ]
    
    // MARK: - Actions
    
    private func toggleBold() {
        let oldValue = layer.isBold
        layer.isBold.toggle()
        undoManager.recordAction(.toggleTextStyle(layer, "bold", oldValue, layer.isBold))
        
        Task {
            await processor.updateCompositeImage()
        }
    }
    
    private func toggleItalic() {
        let oldValue = layer.isItalic
        layer.isItalic.toggle()
        undoManager.recordAction(.toggleTextStyle(layer, "italic", oldValue, layer.isItalic))
        
        Task {
            await processor.updateCompositeImage()
        }
    }
    
    private func toggleUnderline() {
        let oldValue = layer.isUnderlined
        layer.isUnderlined.toggle()
        undoManager.recordAction(.toggleTextStyle(layer, "underline", oldValue, layer.isUnderlined))
        
        Task {
            await processor.updateCompositeImage()
        }
    }
    
    private func changeTextColor(to color: Color) {
        let oldColor = layer.textColor
        layer.textColor = color

        // 如果改为粉色标题文字，自动关闭模糊效果
        if color == Color(CyberPunkStyle.neonPink) {
            layer.hasBlur = false
            layer.blurRadius = 0
        }

        undoManager.recordAction(.changeTextColor(layer, oldColor, color))

        Task {
            await processor.updateCompositeImage()
        }
    }
    
    private func increaseFontSize() {
        let oldSize = layer.fontSize
        layer.fontSize = min(layer.fontSize + 2, 72)
        undoManager.recordAction(.changeFontSize(layer, oldSize, layer.fontSize))
        
        Task {
            await processor.updateCompositeImage()
        }
    }
    
    private func decreaseFontSize() {
        let oldSize = layer.fontSize
        layer.fontSize = max(layer.fontSize - 2, 8)
        undoManager.recordAction(.changeFontSize(layer, oldSize, layer.fontSize))

        Task {
            await processor.updateCompositeImage()
        }
    }

    // 字体切换
    private func toggleFont() {
        let oldFontName = layer.fontName
        // 在几种常用字体间切换
        let fonts = ["System", "Helvetica", "Arial", "Times New Roman", "Courier"]
        if let currentIndex = fonts.firstIndex(of: layer.fontName) {
            let nextIndex = (currentIndex + 1) % fonts.count
            layer.fontName = fonts[nextIndex]
        } else {
            layer.fontName = "System"
        }

        undoManager.recordAction(.changeFontName(layer, oldFontName, layer.fontName))

        Task {
            await processor.updateCompositeImage()
        }
    }

    // 横竖排列切换
    private func toggleVertical() {
        let oldValue = layer.isVertical
        layer.isVertical.toggle()
        undoManager.recordAction(.toggleTextStyle(layer, "vertical", oldValue, layer.isVertical))

        Task {
            await processor.updateCompositeImage()
        }
    }

    // 高斯模糊切换
    private func toggleBlur() {
        // 粉色标题文字不允许模糊效果
        if isTitleText {
            return
        }

        let oldValue = layer.hasBlur
        layer.hasBlur.toggle()
        undoManager.recordAction(.toggleTextStyle(layer, "blur", oldValue, layer.hasBlur))

        Task {
            await processor.updateCompositeImage()
        }
    }

    // 增加模糊半径
    private func increaseBlurRadius() {
        let oldRadius = layer.blurRadius
        layer.blurRadius = min(layer.blurRadius + 1, 20)
        undoManager.recordAction(.changeBlurRadius(layer, oldRadius, layer.blurRadius))

        Task {
            await processor.updateCompositeImage()
        }
    }

    // 减少模糊半径
    private func decreaseBlurRadius() {
        let oldRadius = layer.blurRadius
        layer.blurRadius = max(layer.blurRadius - 1, 0)
        undoManager.recordAction(.changeBlurRadius(layer, oldRadius, layer.blurRadius))

        Task {
            await processor.updateCompositeImage()
        }
    }

    // 配对控制 - 第四行
    private var pairingControls: some View {
        HStack(spacing: 10) {
            // 配对按钮
            Button(action: {
                togglePairing()
            }) {
                Image(systemName: layer.pairedLayerId != nil ? "link" : "link.badge.plus")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(layer.pairedLayerId != nil ? CyberPunkStyle.neonPink : .white.opacity(0.8))
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(layer.pairedLayerId != nil ? CyberPunkStyle.neonPink.opacity(0.2) : Color.gray.opacity(0.3))
                    )
            }

            // 如果已配对，显示解绑按钮
            if layer.pairedLayerId != nil {
                Button(action: {
                    unpairText()
                }) {
                    Image(systemName: "link.slash")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.red.opacity(0.8))
                        .frame(width: 40, height: 40)
                        .background(
                            Circle()
                                .fill(Color.red.opacity(0.2))
                        )
                }
            }
        }
    }



    // 切换配对状态
    private func togglePairing() {
        if layer.pairedLayerId != nil {
            // 如果已配对，则解绑
            unpairText()
        } else {
            // 如果未配对，则创建配对
            createPairedText()
        }
    }

    // 创建配对文字
    private func createPairedText() {
        // 检测当前文字是否为粉色标题文字
        let isTitleText = layer.textColor == Color(CyberPunkStyle.neonPink)

        if isTitleText {
            // 为标题文字创建深色背景文字
            processor.createPairedBackgroundText(for: layer)
        } else {
            // 为背景文字创建粉色标题文字
            processor.createPairedTitleText(for: layer)
        }
    }

    // 解绑配对文字
    private func unpairText() {
        processor.unpairText(layer)
    }
}
