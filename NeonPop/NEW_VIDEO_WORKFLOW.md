# 新的视频处理流程

## 完整的用户体验流程

### 1. 导入视频
- 用户点击"添加媒体"选择视频
- 显示 `VideoLoadingView` 加载指示器
- 视频加载完成后打开 `VideoTrimView`

### 2. 选择时间段 (VideoTrimView)
- 用户可以预览视频
- 使用滑块选择开始和结束时间（最多10秒）
- 点击"预览抠图效果"按钮

### 3. 单帧预览 (VideoPreviewView)
- 系统提取选择时间段的中间帧
- 对该帧进行背景移除处理
- 显示原图 vs 抠图效果对比
- 用户可以选择：
  - **开始批量抠图**：满意效果，继续处理
  - **重新抠图**：重新处理预览帧
  - **取消**：返回时间选择

### 4. 批量处理
- 显示 `VideoProcessingView` 进度界面
- 处理所有选择时间段的帧
- 对每一帧进行背景移除

### 5. 序列帧预览 (VideoSequencePreviewView) ⭐ 新增
- **播放控制**：可以播放/暂停序列帧动画
- **帧导航**：可以拖动滑块查看任意帧
- **效果对比**：可以切换查看原始帧 vs 抠图帧
- **透明显示**：网格背景显示透明效果
- 用户可以选择：
  - **确认添加到画布**：满意效果，添加到编辑画布
  - **重新处理**：返回重新选择时间段
  - **取消**：放弃整个操作

### 6. 添加到画布
- 序列帧添加到编辑画布
- 自动开始循环播放
- 用户可以进行拖拽、缩放、旋转等编辑操作

## 技术实现

### VideoSequencePreviewView 功能
```swift
struct VideoSequencePreviewView: View {
    let frameSequence: VideoProcessor.VideoFrameSequence
    
    // 播放控制
    @State private var currentFrameIndex = 0
    @State private var isPlaying = false
    @State private var playbackTimer: Timer?
    
    // 显示控制
    @State private var showingOriginal = false
}
```

### 主要特性
1. **实时播放**：使用定时器循环播放序列帧
2. **帧导航**：滑块控制，可以查看任意帧
3. **效果对比**：原始帧 vs 抠图帧切换
4. **透明显示**：网格背景显示透明效果
5. **播放控制**：播放/暂停/重置功能

### 流程控制
```swift
// VideoTrimView 中的新流程
private func confirmTrim() {
    // 批量处理完成后
    if let frameSequence = await videoProcessor.processVideoToFrameSequence(...) {
        // 显示序列帧预览而不是直接完成
        processedSequence = frameSequence
        showingSequencePreview = true
    }
}
```

## 用户体验改进

### 之前的问题
- 批量处理后直接添加到画布，用户无法预知最终效果
- 无法检查所有帧的抠图质量
- 如果效果不满意，需要重新开始整个流程

### 现在的体验
- **可预览**：批量处理后可以播放查看所有帧
- **可检查**：可以逐帧检查抠图质量
- **可重试**：不满意可以返回重新处理
- **透明显示**：清楚看到抠图的透明效果
- **交互友好**：播放控制和帧导航直观易用

## 界面设计

### 播放控制区
- 大号播放/暂停按钮
- 帧计数显示 (帧 X / 总数)
- 帧率显示
- 重置按钮

### 预览区域
- 原始帧/抠图帧切换按钮
- 透明网格背景（仅抠图模式）
- 帧图片显示区域

### 导航控制
- 帧位置滑块
- 拖动提示文字

### 操作按钮
- **确认添加到画布**（主要操作，蓝色）
- **重新处理**（次要操作，橙色）
- **取消**（取消操作，灰色）

## 技术细节

### 播放逻辑
```swift
private func startPlayback() {
    let frameInterval = 1.0 / frameSequence.frameRate
    playbackTimer = Timer.scheduledTimer(withTimeInterval: frameInterval, repeats: true) { _ in
        DispatchQueue.main.async {
            self.currentFrameIndex = (self.currentFrameIndex + 1) % self.frameSequence.frames.count
        }
    }
}
```

### 透明显示
- 使用 Canvas 绘制棋盘格背景
- 仅在抠图模式下显示
- 清楚展示透明效果

### 内存管理
- 及时清理定时器
- onDisappear 时停止播放
- 避免内存泄漏

## 测试建议

1. **导入不同类型的视频**测试兼容性
2. **测试不同时长**的视频片段（1-10秒）
3. **测试播放控制**功能是否流畅
4. **测试帧导航**滑块是否准确
5. **测试效果对比**切换是否正常
6. **测试透明显示**是否清晰
7. **测试重新处理**流程是否正确
