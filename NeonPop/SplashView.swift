//
//  SplashView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI

struct SplashView: View {
    @State private var isAnimating = false
    @State private var showMainView = false
    
    var body: some View {
        if showMainView {
            ContentView(showBackButton: false)
        } else {
            ZStack {
                // 背景
                LinearGradient(
                    colors: [Color.black, CyberPunkStyle.electricBlue.opacity(0.3), Color.black],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 30) {
                    Spacer()
                    
                    // 主标题
                    NeonTextView(
                        text: "NEON POP",
                        color: CyberPunkStyle.neonPink,
                        font: .system(size: 48, weight: .heavy),
                        glowRadius: 20
                    )
                    .scaleEffect(isAnimating ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: isAnimating)
                    
                    // 副标题
                    Text("赛博朋克风格滤镜")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                        .opacity(isAnimating ? 1.0 : 0.5)
                        .animation(.easeInOut(duration: 1.5).delay(0.5), value: isAnimating)
                    
                    Spacer()
                    
                    // 加载指示器
                    VStack(spacing: 15) {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: CyberPunkStyle.neonPink))
                            .scaleEffect(1.5)
                        
                        Text("正在启动...")
                            .font(.system(size: 14))
                            .foregroundColor(.white.opacity(0.7))
                    }
                    .opacity(isAnimating ? 1.0 : 0.0)
                    .animation(.easeInOut(duration: 1).delay(1), value: isAnimating)
                    
                    Spacer()
                }
                .padding()
            }
            .onAppear {
                isAnimating = true
                
                // 2秒后切换到主界面
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                    withAnimation(.easeInOut(duration: 0.5)) {
                        showMainView = true
                    }
                }
            }
        }
    }
}

#Preview {
    SplashView()
}
