//
//  MultiLayerProcessor.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI
import VisionKit
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins

// 通知名称扩展
extension Notification.Name {
    static let layerTransformUpdated = Notification.Name("layerTransformUpdated")
}

@MainActor
class MultiLayerProcessor: ObservableObject {
    @Published var layers: [LayerModel] = []
    @Published var compositeImage: UIImage?
    @Published var isProcessing = false
    @Published var errorMessage: String?
    @Published var showingCutoutEffect = false // 显示抠图特效
    @Published var lastCutoutFailed = false // 跟踪上次抠图是否失败
    @Published var lastFailedLayer: LayerModel? // 记录抠图失败的图层

    // 悬浮菜单全局状态
    @Published var showingFloatingMenu = false
    @Published var floatingMenuLayer: LayerModel?
    @Published var floatingMenuPosition: CGPoint = .zero
    
    private let context = CIContext()
    private let imageProcessor = ImageProcessor()
    private let videoProcessor = VideoProcessor()

    // 视频播放定时器管理
    private var videoTimers: [UUID: Timer] = [:]
    
    // 添加图像图层
    func addImageLayer(from image: UIImage, name: String? = nil) async {
        lastCutoutFailed = false // 重置抠图失败状态

        let layerName = name ?? "图层 \(layers.count + 1)"
        let layer = LayerModel(name: layerName, type: .image)
        layer.originalImage = image

        do {
            // 处理图像（移除背景）
            if #available(iOS 17.0, *) {
                do {
                    let processedImage = try await removeBackground(from: image)
                    layer.processedImage = processedImage
                    lastCutoutFailed = false
                    lastFailedLayer = nil
                } catch {
                    // 如果背景移除失败，使用原图并记录失败状态
                    print("背景移除失败，使用原图: \(error.localizedDescription)")
                    layer.processedImage = image
                    lastCutoutFailed = true
                    lastFailedLayer = layer
                    errorMessage = "自动抠图失败，已使用原图。可点击手动抠图按钮重试。"
                }
            } else {
                layer.processedImage = image
                lastCutoutFailed = true
                lastFailedLayer = layer
                errorMessage = "需要 iOS 17+ 才能使用自动抠图功能"
            }

            layers.append(layer)
            await updateCompositeImage()
        } catch {
            // 这里处理其他可能的错误
            print("添加图层时发生错误: \(error.localizedDescription)")
            // 即使出错也要添加图层，使用原图
            layer.processedImage = image
            lastCutoutFailed = true
            lastFailedLayer = layer
            layers.append(layer)
            await updateCompositeImage()
        }
    }

    // 添加视频图层（处理为序列帧）
    func addVideoLayer(from videoURL: URL, startTime: Double, endTime: Double, name: String? = nil) async {
        let layerName = name ?? "视频 \(layers.count + 1)"
        let layer = LayerModel(name: layerName, type: .video)

        // 设置视频属性
        layer.videoURL = videoURL
        layer.videoStartTime = startTime
        layer.videoEndTime = endTime
        layer.videoDuration = endTime - startTime
        layer.isLooping = true

        // 处理视频为序列帧
        if let frameSequence = await videoProcessor.processVideoToFrameSequence(
            from: videoURL,
            startTime: startTime,
            endTime: endTime
        ) {
            layer.frameSequence = frameSequence.frames
            layer.frameRate = frameSequence.frameRate
            layer.videoThumbnail = frameSequence.frames.first // 使用第一帧作为缩略图
        } else {
            // 如果处理失败，生成基本缩略图
            if let videoInfo = await videoProcessor.getVideoInfo(from: videoURL) {
                layer.videoThumbnail = videoInfo.thumbnail
            }
        }

        layers.append(layer)
        await updateCompositeImage()

        // 启动视频播放定时器
        startVideoPlayback(for: layer)
    }

    // 启动视频播放定时器
    private func startVideoPlayback(for layer: LayerModel) {
        guard layer.type == .video && !layer.frameSequence.isEmpty else { return }

        // 停止现有定时器（如果有）
        stopVideoPlayback(for: layer)

        let frameInterval = max(1.0 / layer.frameRate, 1.0 / 30.0) // 最大30fps

        let timer = Timer.scheduledTimer(withTimeInterval: frameInterval, repeats: true) { timer in
            Task { @MainActor in
                // 检查图层是否还存在
                guard self.layers.contains(where: { $0.id == layer.id }) else {
                    timer.invalidate()
                    self.videoTimers.removeValue(forKey: layer.id)
                    return
                }

                // 更新当前帧索引
                let oldIndex = layer.currentFrameIndex
                layer.currentFrameIndex = (layer.currentFrameIndex + 1) % layer.frameSequence.count
                print("🎬 帧更新 - 图层: \(layer.name), 从帧 \(oldIndex) 到帧 \(layer.currentFrameIndex)/\(layer.frameSequence.count)")

                // 更新合成图像
                await self.updateCompositeImage()
            }
        }

        // 保存定时器引用
        videoTimers[layer.id] = timer

        print("🎬 启动视频播放定时器 - 图层: \(layer.name), 帧率: \(layer.frameRate), 帧数: \(layer.frameSequence.count)")
    }

    // 停止视频播放定时器
    private func stopVideoPlayback(for layer: LayerModel) {
        if let timer = videoTimers[layer.id] {
            timer.invalidate()
            videoTimers.removeValue(forKey: layer.id)
            print("⏹️ 停止视频播放定时器 - 图层: \(layer.name)")
        }
    }

    // 从序列帧创建视频图层
    func addVideoLayerFromFrameSequence(_ frameSequence: VideoProcessor.VideoFrameSequence, name: String? = nil) async {
        let layerName = name ?? "视频 \(layers.count + 1)"
        let layer = LayerModel(name: layerName, type: .video)

        // 设置序列帧数据
        layer.frameSequence = frameSequence.frames
        layer.frameRate = frameSequence.frameRate
        layer.videoDuration = frameSequence.duration
        layer.isLooping = true
        layer.videoThumbnail = frameSequence.frames.first // 使用第一帧作为缩略图

        layers.append(layer)
        await updateCompositeImage()

        // 启动视频播放定时器
        startVideoPlayback(for: layer)
    }

    // 手动重试抠图
    func retryBackgroundRemoval(for layer: LayerModel) async {
        guard let originalImage = layer.originalImage else { return }

        errorMessage = nil

        do {
            if #available(iOS 17.0, *) {
                let processedImage = try await removeBackground(from: originalImage)

                // 更新图层
                if let index = layers.firstIndex(where: { $0.id == layer.id }) {
                    layers[index].processedImage = processedImage
                    lastCutoutFailed = false
                    lastFailedLayer = nil
                    await updateCompositeImage()
                }
            } else {
                errorMessage = "需要 iOS 17+ 才能使用抠图功能"
            }
        } catch {
            print("手动抠图重试失败: \(error.localizedDescription)")
            errorMessage = "抠图重试失败: \(error.localizedDescription)"
            lastCutoutFailed = true
        }
    }
    
    // 添加文字图层
    func addTextLayer(text: String, color: Color = .white, fontSize: CGFloat = 24) {
        let layerName = "文字 \(layers.count + 1)"
        let layer = LayerModel(name: layerName, type: .text)
        layer.text = text
        layer.textColor = color
        layer.fontSize = fontSize
        layer.fontName = "System"
        layer.isBold = false
        layer.isItalic = false
        layer.isUnderlined = false
        layer.textAlignment = .center
        layer.isVertical = false
        layer.hasBlur = false
        layer.blurRadius = 3

        layers.append(layer)
        Task {
            await updateCompositeImage()
        }
    }
    
    // 移除图层
    func removeLayer(_ layer: LayerModel) {
        // 如果要删除的图层有配对，先解绑
        if let pairedId = layer.pairedLayerId {
            unpairText(layer)
        }

        // 如果是视频图层，停止播放定时器
        if layer.type == .video {
            stopVideoPlayback(for: layer)
        }

        if let index = layers.firstIndex(where: { $0.id == layer.id }) {
            layers.remove(at: index)
            Task {
                await updateCompositeImage()
            }
        }
    }

    // 为标题文字创建配对的背景文字
    func createPairedBackgroundText(for titleLayer: LayerModel) {
        guard titleLayer.type == .text, titleLayer.pairedLayerId == nil else { return }

        let backgroundLayer = LayerModel(name: "背景文字 \(layers.count + 1)", type: .text)
        backgroundLayer.text = titleLayer.text
        backgroundLayer.textColor = Color(CyberPunkStyle.decorativeBlue)
        backgroundLayer.fontSize = titleLayer.fontSize
        backgroundLayer.fontName = titleLayer.fontName
        backgroundLayer.isBold = titleLayer.isBold
        backgroundLayer.isItalic = titleLayer.isItalic
        backgroundLayer.isUnderlined = titleLayer.isUnderlined
        backgroundLayer.textAlignment = titleLayer.textAlignment
        backgroundLayer.isVertical = titleLayer.isVertical

        backgroundLayer.hasBlur = true
        backgroundLayer.blurRadius = 5
        backgroundLayer.isPairedPrimary = false

        // 设置配对关系
        titleLayer.pairedLayerId = backgroundLayer.id
        backgroundLayer.pairedLayerId = titleLayer.id

        // 计算背景文字位置（在标题文字右下方）
        let offset: CGFloat = 20
        backgroundLayer.transform.position = CGPoint(
            x: titleLayer.transform.position.x + offset,
            y: titleLayer.transform.position.y + offset
        )

        layers.append(backgroundLayer)

        Task {
            await updateCompositeImage()
        }
    }

    // 为背景文字创建配对的标题文字
    func createPairedTitleText(for backgroundLayer: LayerModel) {
        guard backgroundLayer.type == .text, backgroundLayer.pairedLayerId == nil else { return }

        let titleLayer = LayerModel(name: "标题文字 \(layers.count + 1)", type: .text)
        titleLayer.text = backgroundLayer.text
        titleLayer.textColor = Color(CyberPunkStyle.neonPink)
        titleLayer.fontSize = backgroundLayer.fontSize
        titleLayer.fontName = backgroundLayer.fontName
        titleLayer.isBold = backgroundLayer.isBold
        titleLayer.isItalic = backgroundLayer.isItalic
        titleLayer.isUnderlined = backgroundLayer.isUnderlined
        titleLayer.textAlignment = backgroundLayer.textAlignment
        titleLayer.isVertical = backgroundLayer.isVertical

        titleLayer.hasBlur = false
        titleLayer.blurRadius = 0
        titleLayer.isPairedPrimary = true

        // 设置配对关系
        backgroundLayer.pairedLayerId = titleLayer.id
        titleLayer.pairedLayerId = backgroundLayer.id

        // 计算标题文字位置（在背景文字左上方）
        let offset: CGFloat = 20
        titleLayer.transform.position = CGPoint(
            x: backgroundLayer.transform.position.x - offset,
            y: backgroundLayer.transform.position.y - offset
        )

        layers.append(titleLayer)

        Task {
            await updateCompositeImage()
        }
    }

    // 解绑配对文字
    func unpairText(_ layer: LayerModel) {
        guard let pairedId = layer.pairedLayerId else { return }

        // 找到配对的图层
        if let pairedLayer = layers.first(where: { $0.id == pairedId }) {
            // 清除配对关系
            layer.pairedLayerId = nil
            pairedLayer.pairedLayerId = nil
        }

        Task {
            await updateCompositeImage()
        }
    }

    // 同步配对文字的位置（当移动其中一个时调用）
    func syncPairedTextPosition(_ movedLayer: LayerModel) {
        guard let pairedId = movedLayer.pairedLayerId,
              let pairedLayer = layers.first(where: { $0.id == pairedId }) else { return }

        let offset: CGFloat = 20

        if movedLayer.isPairedPrimary {
            // 移动的是主要文字（标题），更新背景文字位置
            pairedLayer.transform.position = CGPoint(
                x: movedLayer.transform.position.x + offset,
                y: movedLayer.transform.position.y + offset
            )
        } else {
            // 移动的是背景文字，更新标题文字位置
            pairedLayer.transform.position = CGPoint(
                x: movedLayer.transform.position.x - offset,
                y: movedLayer.transform.position.y - offset
            )
        }
    }
    
    // 移动图层
    func moveLayer(from sourceIndex: Int, to destinationIndex: Int) {
        guard sourceIndex != destinationIndex,
              sourceIndex >= 0, sourceIndex < layers.count,
              destinationIndex >= 0, destinationIndex <= layers.count else { return }

        let layer = layers.remove(at: sourceIndex)
        let insertIndex = destinationIndex > sourceIndex ? destinationIndex - 1 : destinationIndex
        layers.insert(layer, at: insertIndex)

        Task {
            await updateCompositeImage()
        }
    }

    // 上移图层（向前移动，显示在上层）
    func moveLayerUp(_ layer: LayerModel) {
        guard let currentIndex = layers.firstIndex(where: { $0.id == layer.id }),
              currentIndex < layers.count - 1 else { return }

        layers.swapAt(currentIndex, currentIndex + 1)
        Task {
            await updateCompositeImage()
        }
    }

    // 下移图层（向后移动，显示在下层）
    func moveLayerDown(_ layer: LayerModel) {
        guard let currentIndex = layers.firstIndex(where: { $0.id == layer.id }),
              currentIndex > 0 else { return }

        layers.swapAt(currentIndex, currentIndex - 1)
        Task {
            await updateCompositeImage()
        }
    }
    
    // 更新图层变换
    func updateLayerTransform(_ layer: LayerModel, transform: LayerTransform) {
        if let index = layers.firstIndex(where: { $0.id == layer.id }) {
            layers[index].transform = transform
            Task {
                await updateCompositeImage()
            }

            // 通知需要自动保存
            NotificationCenter.default.post(name: .layerTransformUpdated, object: layer)
        }
    }
    
    // 切换图层可见性
    func toggleLayerVisibility(_ layer: LayerModel) {
        if let index = layers.firstIndex(where: { $0.id == layer.id }) {
            layers[index].isVisible.toggle()
            Task {
                await updateCompositeImage()
            }
        }
    }
    
    // 选择图层
    func selectLayer(_ layer: LayerModel) {
        // 取消所有图层的选择
        for i in 0..<layers.count {
            layers[i].isSelected = false
        }
        
        // 选择指定图层
        if let index = layers.firstIndex(where: { $0.id == layer.id }) {
            layers[index].isSelected = true
        }
    }
    
    // 取消所有选择
    func deselectAllLayers() {
        for i in 0..<layers.count {
            layers[i].isSelected = false
        }
    }
    
    // 获取选中的图层
    func getSelectedLayer() -> LayerModel? {
        return layers.first { $0.isSelected }
    }

    // 切换图层颜色变换
    func toggleLayerColorTransform(_ layer: LayerModel) {
        if let index = layers.firstIndex(where: { $0.id == layer.id }) {
            layers[index].isColorTransformed.toggle()
            Task {
                await updateCompositeImage()
            }
        }
    }

    // 显示悬浮菜单
    func showFloatingMenu(for layer: LayerModel, at position: CGPoint) {
        // 如果当前已经显示的是同一个图层的菜单，则隐藏
        if showingFloatingMenu && floatingMenuLayer?.id == layer.id {
            hideFloatingMenu()
            return
        }

        // 显示新的悬浮菜单
        floatingMenuLayer = layer
        floatingMenuPosition = position
        showingFloatingMenu = true
    }

    // 显示悬浮菜单（带边界检查）
    func showFloatingMenu(for layer: LayerModel, at position: CGPoint, canvasSize: CGSize) {
        // 如果当前已经显示的是同一个图层的菜单，则隐藏
        if showingFloatingMenu && floatingMenuLayer?.id == layer.id {
            hideFloatingMenu()
            return
        }

        // 悬浮菜单的估计尺寸（根据实际菜单大小调整）
        let menuWidth: CGFloat = 220 // 菜单宽度
        let menuHeight: CGFloat = 120 // 菜单高度
        let margin: CGFloat = 20 // 边距

        var adjustedPosition = position

        // 水平边界检查
        let canvasHalfWidth = canvasSize.width / 2
        if position.x - menuWidth/2 < -canvasHalfWidth + margin {
            // 左边界溢出，调整到左边界内
            adjustedPosition.x = -canvasHalfWidth + margin + menuWidth/2
        } else if position.x + menuWidth/2 > canvasHalfWidth - margin {
            // 右边界溢出，调整到右边界内
            adjustedPosition.x = canvasHalfWidth - margin - menuWidth/2
        }

        // 垂直边界检查
        let canvasHalfHeight = canvasSize.height / 2
        if position.y - menuHeight/2 < -canvasHalfHeight + margin {
            // 上边界溢出，调整到下方显示
            adjustedPosition.y = position.y + menuHeight + 20
        }

        // 显示新的悬浮菜单
        floatingMenuLayer = layer
        floatingMenuPosition = adjustedPosition
        showingFloatingMenu = true
    }

    // 隐藏悬浮菜单
    func hideFloatingMenu() {
        showingFloatingMenu = false
        floatingMenuLayer = nil
    }

    // 开始内联文字编辑
    func startInlineTextEditing(for layer: LayerModel) {
        // 隐藏悬浮菜单
        hideFloatingMenu()

        // 发送通知开始编辑
        NotificationCenter.default.post(name: .startInlineEditing, object: layer)
    }

    // 结束内联文字编辑
    func endInlineTextEditing() {
        NotificationCenter.default.post(name: .endInlineEditing, object: nil)
    }
    
    // 更新合成图像
    func updateCompositeImage() async {
        guard !layers.isEmpty else {
            compositeImage = nil
            return
        }
        
        // 计算画布大小（基于第一个图像图层）
        let canvasSize = getCanvasSize()
        
        let renderer = UIGraphicsImageRenderer(size: canvasSize)
        
        let image = renderer.image { context in
            let rect = CGRect(origin: .zero, size: canvasSize)
            
            // 绘制赛博朋克背景
            drawCyberPunkBackground(in: context, rect: rect)
            
            // 按顺序绘制所有可见图层
            for layer in layers where layer.isVisible {
                drawLayer(layer, in: context, canvasRect: rect)
            }
        }
        
        compositeImage = image
    }
    
    // 获取画布大小 - 支持动态大小计算
    private func getCanvasSize() -> CGSize {
        // 如果有图层，计算包含所有图层的最小画布大小
        if !layers.isEmpty {
            var minX: CGFloat = 0
            var maxX: CGFloat = 0
            var minY: CGFloat = 0
            var maxY: CGFloat = 0

            for layer in layers where layer.isVisible {
                let position = layer.transform.position
                let scale = layer.transform.scale

                if layer.type == .image, let image = layer.processedImage ?? layer.originalImage {
                    let imageSize = image.size
                    let scaledWidth = imageSize.width * scale
                    let scaledHeight = imageSize.height * scale

                    let layerMinX = position.x - scaledWidth / 2
                    let layerMaxX = position.x + scaledWidth / 2
                    let layerMinY = position.y - scaledHeight / 2
                    let layerMaxY = position.y + scaledHeight / 2

                    minX = min(minX, layerMinX)
                    maxX = max(maxX, layerMaxX)
                    minY = min(minY, layerMinY)
                    maxY = max(maxY, layerMaxY)
                } else if layer.type == .video {
                    // 处理视频图层
                    let videoSize: CGSize
                    if !layer.frameSequence.isEmpty {
                        videoSize = layer.frameSequence[0].size
                    } else if let thumbnail = layer.videoThumbnail {
                        videoSize = thumbnail.size
                    } else {
                        videoSize = CGSize(width: 200, height: 150) // 默认尺寸
                    }

                    let scaledWidth = videoSize.width * scale
                    let scaledHeight = videoSize.height * scale

                    let layerMinX = position.x - scaledWidth / 2
                    let layerMaxX = position.x + scaledWidth / 2
                    let layerMinY = position.y - scaledHeight / 2
                    let layerMaxY = position.y + scaledHeight / 2

                    minX = min(minX, layerMinX)
                    maxX = max(maxX, layerMaxX)
                    minY = min(minY, layerMinY)
                    maxY = max(maxY, layerMaxY)
                } else if layer.type == .text, let text = layer.text {
                    // 估算文字大小
                    let estimatedWidth = CGFloat(text.count) * layer.fontSize * 0.6 * scale
                    let estimatedHeight = layer.fontSize * scale

                    let layerMinX = position.x - estimatedWidth / 2
                    let layerMaxX = position.x + estimatedWidth / 2
                    let layerMinY = position.y - estimatedHeight / 2
                    let layerMaxY = position.y + estimatedHeight / 2

                    minX = min(minX, layerMinX)
                    maxX = max(maxX, layerMaxX)
                    minY = min(minY, layerMinY)
                    maxY = max(maxY, layerMaxY)
                }
            }

            // 添加边距并确保最小尺寸
            let padding: CGFloat = 100
            let width = max(maxX - minX + padding * 2, 375)
            let height = max(maxY - minY + padding * 2, 400)

            return CGSize(width: width, height: height)
        }

        // 如果没有图层，使用默认大小
        return CGSize(width: 375, height: 400)
    }
    
    // 绘制赛博朋克背景
    private func drawCyberPunkBackground(in context: UIGraphicsImageRendererContext, rect: CGRect) {
        // 电蓝色背景
        UIColor(CyberPunkStyle.electricBlue).setFill()
        context.fill(rect)
        
        // 添加渐变效果
        let gradient = CGGradient(colorsSpace: CGColorSpaceCreateDeviceRGB(),
                                colors: [
                                    UIColor(CyberPunkStyle.electricBlue).cgColor,
                                    UIColor(CyberPunkStyle.electricBlue.opacity(0.7)).cgColor,
                                    UIColor(CyberPunkStyle.electricBlue).cgColor
                                ] as CFArray,
                                locations: [0.0, 0.5, 1.0])!
        
        context.cgContext.drawLinearGradient(gradient,
                                           start: CGPoint(x: 0, y: 0),
                                           end: CGPoint(x: rect.width, y: rect.height),
                                           options: [])
    }
    
    // 绘制单个图层
    private func drawLayer(_ layer: LayerModel, in context: UIGraphicsImageRendererContext, canvasRect: CGRect) {
        context.cgContext.saveGState()
        
        // 应用变换
        let transform = layer.transform
        context.cgContext.translateBy(x: canvasRect.midX + transform.position.x,
                                    y: canvasRect.midY + transform.position.y)
        context.cgContext.scaleBy(x: transform.scale, y: transform.scale)
        context.cgContext.rotate(by: CGFloat(transform.rotation))
        context.cgContext.setAlpha(CGFloat(transform.opacity))
        
        switch layer.type {
        case .image:
            if let image = layer.processedImage ?? layer.originalImage {
                let imageRect = CGRect(x: -image.size.width/2, y: -image.size.height/2,
                                     width: image.size.width, height: image.size.height)
                image.draw(in: imageRect)
            }

        case .video:
            // 绘制当前视频帧
            let currentFrame: UIImage?
            if !layer.frameSequence.isEmpty {
                let frameIndex = min(layer.currentFrameIndex, layer.frameSequence.count - 1)
                currentFrame = layer.frameSequence[frameIndex]
            } else {
                currentFrame = layer.videoThumbnail
            }

            if let frame = currentFrame {
                let imageRect = CGRect(x: -frame.size.width/2, y: -frame.size.height/2,
                                     width: frame.size.width, height: frame.size.height)
                frame.draw(in: imageRect)
            }

        case .text:
            drawAdvancedText(layer, in: context)

        case .effect:
            // 特效图层的绘制逻辑
            break
        }
        
        context.cgContext.restoreGState()
    }
    
    // 绘制文字
    private func drawText(_ text: String, color: Color, fontSize: CGFloat, in context: UIGraphicsImageRendererContext) {
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: fontSize, weight: .bold),
            .foregroundColor: UIColor(color)
        ]

        let attributedString = NSAttributedString(string: text, attributes: attributes)
        let textSize = attributedString.size()
        let textRect = CGRect(x: -textSize.width/2, y: -textSize.height/2,
                            width: textSize.width, height: textSize.height)

        attributedString.draw(in: textRect)
    }

    // 绘制高级文字（支持新的文字属性）
    private func drawAdvancedText(_ layer: LayerModel, in context: UIGraphicsImageRendererContext) {
        guard let text = layer.text else { return }

        // 创建字体
        var font: UIFont
        if layer.fontName == "System" {
            font = UIFont.systemFont(ofSize: layer.fontSize)
        } else {
            font = UIFont(name: layer.fontName, size: layer.fontSize) ?? UIFont.systemFont(ofSize: layer.fontSize)
        }

        // 应用粗体和斜体
        var traits: UIFontDescriptor.SymbolicTraits = []
        if layer.isBold {
            traits.insert(.traitBold)
        }
        if layer.isItalic {
            traits.insert(.traitItalic)
        }

        if !traits.isEmpty {
            if let descriptor = font.fontDescriptor.withSymbolicTraits(traits) {
                font = UIFont(descriptor: descriptor, size: layer.fontSize)
            }
        }

        // 创建属性字典
        var attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: UIColor(layer.textColor)
        ]

        // 添加下划线
        if layer.isUnderlined {
            attributes[.underlineStyle] = NSUnderlineStyle.single.rawValue
        }

        // 创建属性字符串
        let attributedString = NSMutableAttributedString(string: text, attributes: attributes)

        // 设置段落样式（对齐方式）
        let paragraphStyle = NSMutableParagraphStyle()
        switch layer.textAlignment {
        case .leading:
            paragraphStyle.alignment = .left
        case .center:
            paragraphStyle.alignment = .center
        case .trailing:
            paragraphStyle.alignment = .right
        }

        attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: text.count))

        // 计算文字大小和位置
        let textSize = attributedString.size()
        var textRect = CGRect(x: -textSize.width/2, y: -textSize.height/2,
                            width: textSize.width, height: textSize.height)

        // 如果是垂直布局，需要旋转上下文
        if layer.isVertical {
            context.cgContext.saveGState()
            context.cgContext.rotate(by: -CGFloat.pi / 2)
            textRect = CGRect(x: -textSize.height/2, y: -textSize.width/2,
                            width: textSize.height, height: textSize.width)
        }

        // 绘制文字 - 支持高斯模糊效果
        if layer.hasBlur && layer.blurRadius > 0 {
            // 调整模糊半径以匹配SwiftUI效果
            let adjustedBlurRadius = layer.blurRadius * 1.5 // 1.5倍系数匹配SwiftUI blur效果

            // 创建一个足够大的临时画布来容纳模糊效果
            let blurPadding = adjustedBlurRadius * 3 // 增加边距以容纳模糊扩散
            let tempSize = CGSize(width: textSize.width + blurPadding * 2,
                                height: textSize.height + blurPadding * 2)
            let tempRenderer = UIGraphicsImageRenderer(size: tempSize)

            let textImage = tempRenderer.image { tempContext in
                // 在临时画布中心绘制文字
                let centeredRect = CGRect(x: blurPadding, y: blurPadding,
                                        width: textSize.width, height: textSize.height)
                attributedString.draw(in: centeredRect)
            }

            // 应用高斯模糊（使用调整后的半径）
            if let blurredImage = applyGaussianBlur(to: textImage, radius: adjustedBlurRadius) {
                // 计算绘制位置
                let drawRect = CGRect(x: textRect.origin.x - blurPadding,
                                    y: textRect.origin.y - blurPadding,
                                    width: tempSize.width, height: tempSize.height)
                blurredImage.draw(in: drawRect)
            } else {
                // 如果模糊失败，绘制原始文字
                attributedString.draw(in: textRect)
            }
        } else {
            // 正常绘制文字
            attributedString.draw(in: textRect)
        }

        if layer.isVertical {
            context.cgContext.restoreGState()
        }
    }

    // 应用高斯模糊 - 改进版本以匹配SwiftUI效果
    private func applyGaussianBlur(to image: UIImage, radius: CGFloat) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let ciImage = CIImage(cgImage: cgImage)
        let filter = CIFilter.gaussianBlur()
        filter.inputImage = ciImage
        filter.radius = Float(radius)

        guard let outputImage = filter.outputImage else { return nil }

        // 扩展输出区域以包含完整的模糊效果
        let extent = ciImage.extent
        let expandedExtent = extent.insetBy(dx: -radius * 3, dy: -radius * 3)

        guard let outputCGImage = context.createCGImage(outputImage, from: expandedExtent) else { return nil }

        return UIImage(cgImage: outputCGImage, scale: image.scale, orientation: image.imageOrientation)
    }



    // 移除背景（复用ImageProcessor的逻辑）
    private func removeBackground(from image: UIImage) async throws -> UIImage {
        guard #available(iOS 17.0, *) else {
            throw ImageProcessingError.unsupportedVersion
        }

        guard let cgImage = image.cgImage else {
            throw ImageProcessingError.invalidImage
        }

        // 添加超时保护
        return try await withThrowingTaskGroup(of: UIImage.self) { group in
            group.addTask {
                try await withCheckedThrowingContinuation { continuation in
                    var hasResumed = false // 防止多次恢复
                    let requestHandler = VNImageRequestHandler(cgImage: cgImage, options: [:])

                    let request = VNGenerateForegroundInstanceMaskRequest { request, error in
                        guard !hasResumed else { return } // 防止重复调用

                        if let error = error {
                            hasResumed = true
                            continuation.resume(throwing: error)
                            return
                        }

                        guard let result = request.results?.first as? VNInstanceMaskObservation else {
                            hasResumed = true
                            continuation.resume(throwing: ImageProcessingError.noSubjectFound)
                            return
                        }

                        do {
                            let maskedPixelBuffer = try result.generateMaskedImage(
                                ofInstances: result.allInstances,
                                from: requestHandler,
                                croppedToInstancesExtent: false
                            )

                            let correctedImage = self.correctImageOrientation(from: maskedPixelBuffer, originalImage: image)
                            hasResumed = true
                            continuation.resume(returning: correctedImage)
                        } catch {
                            hasResumed = true
                            continuation.resume(throwing: error)
                        }
                    }

                    do {
                        try requestHandler.perform([request])
                    } catch {
                        guard !hasResumed else { return }
                        hasResumed = true
                        continuation.resume(throwing: error)
                    }
                }
            }

            // 添加超时任务
            group.addTask {
                try await Task.sleep(nanoseconds: 10_000_000_000) // 10秒超时
                throw ImageProcessingError.processingTimeout
            }

            // 返回第一个完成的任务结果
            guard let result = try await group.next() else {
                throw ImageProcessingError.processingTimeout
            }

            group.cancelAll() // 取消其他任务
            return result
        }
    }
    
    // 修正图像方向
    private func correctImageOrientation(from pixelBuffer: CVPixelBuffer, originalImage: UIImage) -> UIImage {
        let ciImage = CIImage(cvPixelBuffer: pixelBuffer)
        
        var transformedImage = ciImage
        switch originalImage.imageOrientation {
        case .up:
            break
        case .down:
            transformedImage = ciImage.oriented(.down)
        case .left:
            transformedImage = ciImage.oriented(.left)
        case .right:
            transformedImage = ciImage.oriented(.right)
        case .upMirrored:
            transformedImage = ciImage.oriented(.upMirrored)
        case .downMirrored:
            transformedImage = ciImage.oriented(.downMirrored)
        case .leftMirrored:
            transformedImage = ciImage.oriented(.leftMirrored)
        case .rightMirrored:
            transformedImage = ciImage.oriented(.rightMirrored)
        @unknown default:
            break
        }
        
        guard let cgImage = context.createCGImage(transformedImage, from: transformedImage.extent) else {
            return originalImage
        }
        
        return UIImage(cgImage: cgImage, scale: originalImage.scale, orientation: .up)
    }
}
